.acoes ::ng-deep .icone  {
  display: inline-block;
  fill: #fff;
  vertical-align: middle;
  svg {
    width: 24px !important;
    height: 24px !important;
  }
}


.card.contato {
  >.card-body{
    padding-right: 0;
  }
  .ultima-visita{
    top: 30px;position: relative;
  }
}

.plano-info{
  border-left: 1px solid #e5e5e5;
  margin-top: -25px;
  margin-bottom: -25px;
  padding-left: 0;

  .card-box{
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;

    &.plano{
      border-bottom: 1px solid #e5e5e5;
      margin-top: 21px;
      padding-bottom: 21px;
      border-radius: 0;
      padding-left: 20px;
      display: table;
      width: 100%;
    }

    p-dropdown{
      display: inline-block;
      position: relative;
      top: 10px;
      margin-left: 15px;
    }
  }

  .pontos{

    span {
      font-size: 40px;
      font-weight: 500;
      line-height: 0.5em;
      position: relative;
      top: 3px;
    }
  }

  label{
    margin-bottom: 0;
    line-height: 18px;
    font-size: 16px;
  }

  .por-selo{
    .lista-selos{
      position: relative;   top: -10px;     display: block;
    }
    label{
      line-height: 30px;
      width: 56px;
    }
  }
}

.acoes .btn{
  margin-right:15px
}

.fa-whatsapp{
  font-size: 25px;
}

@media (max-width: 768px){
  .acoes ::ng-deep .icone.tam1,.icone {
    width: 100% !important;
    height: 25px !important;
    svg {
      width: 25px !important;
      height: 25px !important;
    }
  }

  .card.contato {
    >.card-body{
      padding: 1rem !important;
    }

    margin-bottom: 0px;
  }
  .plano-info{
    border: none;
    padding-top:0;
    margin-top: 0;
    margin-bottom:0;

    .card-box{
      &.plano{
        padding-bottom: 30px;
      }

      &.por-selo{
        padding-top: 10px;
      }
      p-dropdown{
        margin-left:0;
      }
    }

  }

  .acoes .btn{
    padding: 10px;
    float: left;
    width: 100%;
    height: 100%;
    margin-top: 5px;
    margin-right: 0px;
  }

  .card-box {
    padding: 1rem;
    .lista-selos{
      width: 100%;
      display: table;
      padding-top: 10px;
    }
  }
}


td a{
  color: inherit;
}

.tamanho-botao {
  width: 135px;
}

.botao-fundo {
  border: none;
  &.verde {
    background: url(/assets/fidelidade/Banner_01.png)
  }
  &.azul {
    background: url(/assets/fidelidade/Banner_02.png)
  }

  &.roxo {
    background: url(/assets/fidelidade/Banner_03.png)
  }

  &.laranja {
    background: url(/assets/fidelidade/Banner_04.png)
  }
}

// Estilos para o relatório de diagnóstico
.relatorio-container {
  position: relative;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #17a2b8;
    border-radius: 4px;

    &:hover {
      background: #138496;
    }
  }

  // Melhorar a legibilidade dos emojis e ícones
  pre {
    // Destacar diferentes tipos de log com cores sutis
    .emoji-info { color: #17a2b8; }
    .emoji-warning { color: #ffc107; }
    .emoji-error { color: #dc3545; }
    .emoji-success { color: #28a745; }
  }
}

// Animação suave para aparecer o relatório
.card.shadow-sm {
  animation: slideDown 0.3s ease-out;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Melhorar os botões
.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;

  &:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
  }
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;

  &:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }
}

// Responsividade para o relatório
@media (max-width: 768px) {
  .relatorio-container {
    max-height: 300px !important;
    font-size: 10px !important;
    padding: 10px !important;

    pre {
      font-size: 10px !important;
      line-height: 1.3 !important;
    }
  }

  .card-footer {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;

      small {
        margin-bottom: 10px;
      }

      div {
        width: 100%;
        text-align: center;

        .btn {
          width: 45%;
          margin: 0 2.5%;
        }
      }
    }
  }
}

// Melhorar o header do card
.card-header.bg-info {
  background: linear-gradient(135deg, #17a2b8, #138496) !important;
  border-bottom: 2px solid #138496;

  h6 {
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  .btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
      transform: scale(1.05);
    }
  }
}
