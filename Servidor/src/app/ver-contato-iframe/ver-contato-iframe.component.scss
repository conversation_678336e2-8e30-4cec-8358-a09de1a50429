.acoes ::ng-deep .icone  {
  display: inline-block;
  fill: #fff;
  vertical-align: middle;
  svg {
    width: 24px !important;
    height: 24px !important;
  }
}


.card.contato {
  >.card-body{
    padding-right: 0;
  }
  .ultima-visita{
    top: 30px;position: relative;
  }
}

.plano-info{
  border-left: 1px solid #e5e5e5;
  margin-top: -25px;
  margin-bottom: -25px;
  padding-left: 0;

  .card-box{
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;

    &.plano{
      border-bottom: 1px solid #e5e5e5;
      margin-top: 21px;
      padding-bottom: 21px;
      border-radius: 0;
      padding-left: 20px;
      display: table;
      width: 100%;
    }

    p-dropdown{
      display: inline-block;
      position: relative;
      top: 10px;
      margin-left: 15px;
    }
  }

  .pontos{

    span {
      font-size: 40px;
      font-weight: 500;
      line-height: 0.5em;
      position: relative;
      top: 3px;
    }
  }

  label{
    margin-bottom: 0;
    line-height: 18px;
    font-size: 16px;
  }

  .por-selo{
    .lista-selos{
      position: relative;   top: -10px;     display: block;
    }
    label{
      line-height: 30px;
      width: 56px;
    }
  }
}

.acoes .btn{
  margin-right:15px
}

.fa-whatsapp{
  font-size: 25px;
}

@media (max-width: 768px){
  .acoes ::ng-deep .icone.tam1,.icone {
    width: 100% !important;
    height: 25px !important;
    svg {
      width: 25px !important;
      height: 25px !important;
    }
  }

  .card.contato {
    >.card-body{
      padding: 1rem !important;
    }

    margin-bottom: 0px;
  }
  .plano-info{
    border: none;
    padding-top:0;
    margin-top: 0;
    margin-bottom:0;

    .card-box{
      &.plano{
        padding-bottom: 30px;
      }

      &.por-selo{
        padding-top: 10px;
      }
      p-dropdown{
        margin-left:0;
      }
    }

  }

  .acoes .btn{
    padding: 10px;
    float: left;
    width: 100%;
    height: 100%;
    margin-top: 5px;
    margin-right: 0px;
  }

  .card-box {
    padding: 1rem;
    .lista-selos{
      width: 100%;
      display: table;
      padding-top: 10px;
    }
  }
}


td a{
  color: inherit;
}

.tamanho-botao {
  width: 135px;
}

.botao-fundo {
  border: none;
  &.verde {
    background: url(/assets/fidelidade/Banner_01.png)
  }
  &.azul {
    background: url(/assets/fidelidade/Banner_02.png)
  }

  &.roxo {
    background: url(/assets/fidelidade/Banner_03.png)
  }

  &.laranja {
    background: url(/assets/fidelidade/Banner_04.png)
  }
}

// Estilos para o relatório de diagnóstico
.relatorio-container {
  position: relative;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #17a2b8;
    border-radius: 4px;

    &:hover {
      background: #138496;
    }
  }

  .log-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    line-height: 1.6 !important;
    font-size: 12px;
    color: #f8f9fa;
  }
}

// Estilos para logs formatados
.log-formatted {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;

  .log-success {
    background: rgba(40, 167, 69, 0.15);
    border-left: 4px solid #28a745;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    color: #90ee90;
  }

  .log-error {
    background: rgba(220, 53, 69, 0.15);
    border-left: 4px solid #dc3545;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    color: #ffb3b3;
  }

  .log-warning {
    background: rgba(255, 193, 7, 0.15);
    border-left: 4px solid #ffc107;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    color: #fff3cd;
  }

  .log-processing {
    background: rgba(23, 162, 184, 0.15);
    border-left: 4px solid #17a2b8;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    color: #b3e5fc;
  }

  .log-info {
    background: rgba(108, 117, 125, 0.15);
    border-left: 4px solid #6c757d;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 0 4px 4px 0;
    color: #e2e3e5;
  }

  .log-header {
    color: #ffc107;
    font-weight: bold;
    font-size: 14px;
    margin: 12px 0 8px 0;
    text-shadow: 0 0 2px rgba(255, 193, 7, 0.5);
  }

  .log-title {
    color: #17a2b8;
    font-weight: bold;
    font-size: 16px;
    margin: 16px 0 12px 0;
    text-shadow: 0 0 2px rgba(23, 162, 184, 0.5);
  }

  .log-separator {
    color: #6c757d;
    margin: 8px 0;
    opacity: 0.7;
  }

  .log-divider {
    color: #495057;
    margin: 6px 0;
    opacity: 0.5;
  }

  .log-subdivider {
    color: #343a40;
    margin: 4px 0;
    opacity: 0.4;
  }

  .log-time {
    color: #b3e5fc;
    font-weight: 500;
  }

  .log-user {
    color: #ffb3ff;
    font-weight: 500;
  }

  .log-message {
    color: #e6ffe6;
    font-weight: 400;
    margin-left: 4px;
  }

  .log-phone {
    color: #ffffb3;
    font-weight: 500;
  }

  .log-duration {
    color: #ffcc99;
    font-weight: 500;
  }
}
}

// Animação suave para aparecer o relatório
.card.shadow-sm {
  animation: slideDown 0.3s ease-out;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Melhorar os botões
.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;

  &:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
  }
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;

  &:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
  }
}

// Responsividade para o relatório
@media (max-width: 768px) {
  .relatorio-container {
    max-height: 300px !important;
    font-size: 10px !important;
    padding: 10px !important;

    pre {
      font-size: 10px !important;
      line-height: 1.3 !important;
    }
  }

  .card-footer {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;

      small {
        margin-bottom: 10px;
      }

      div {
        width: 100%;
        text-align: center;

        .btn {
          width: 45%;
          margin: 0 2.5%;
        }
      }
    }
  }
}

// Melhorar o header do card
.card-header.bg-info {
  background: linear-gradient(135deg, #17a2b8, #138496) !important;
  border-bottom: 2px solid #138496;

  h6 {
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  .btn-outline-light {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
      transform: scale(1.05);
    }
  }
}
