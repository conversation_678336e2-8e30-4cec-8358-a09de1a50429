<h4 class="page-title">
  <button class="btn btn-outline-blue btn-rounded" (click)="voltar()" *ngIf="!widget" style="margin-right: 5px;">
    <i class="fa fa-arrow-left ct-point" ></i>
  </button>
  <i class="fe-user "></i> Contato
</h4>

<app-notificacoes-promokit></app-notificacoes-promokit>

<div style="position: absolute;top: 15px;right: 15px;">
  <button class="btn btn-secondary btn-sm mr-1" (click)="exibirTodosLogs();" kendoTooltip title="Ver TODOS os logs do sistema (debug)">
    <i class="fas fa-list"></i>
  </button>
  <button class="btn btn-info mr-2" (click)="executarDiagnostico();" kendoTooltip title="Executar diagnóstico do contato e conexões" *ngIf="contato?.telefone">
    <i class="fas fa-stethoscope"></i>
  </button>
  <button class="btn btn-blue" (click)="recarregue();" kendoTooltip title="Recarregar o cardápio e forçar envio das mensagens."><i class="fas fa-sync-alt"></i></button>
</div>

<app-gerenciador-envio-campanha></app-gerenciador-envio-campanha>

<!-- Seção para exibir relatórios e diagnósticos diretamente na tela -->
<div class="card shadow-sm mt-3" *ngIf="exibirRelatorioNaTela">
  <div class="card-header bg-info text-white d-flex justify-content-between align-items-center py-2">
    <h6 class="mb-0 font-weight-bold">
      <i class="fas fa-terminal mr-2"></i>
      {{tituloRelatorio}}
    </h6>
    <button class="btn btn-sm btn-outline-light rounded-circle p-1" (click)="fecharRelatorio()" kendoTooltip title="Fechar relatório" style="width: 28px; height: 28px;">
      <i class="fas fa-times"></i>
    </button>
  </div>
  <div class="card-body p-0">
    <div class="relatorio-container" style="background: #1e1e1e; color: #f8f9fa; padding: 15px; max-height: 450px; overflow-y: auto; border-left: 4px solid #17a2b8;">
      <pre class="mb-0" style="white-space: pre-wrap; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 12px; line-height: 1.4; color: #f8f9fa;">{{conteudoRelatorio}}</pre>
    </div>
  </div>
  <div class="card-footer bg-light border-top-0" style="background: #f8f9fa !important;">
    <div class="d-flex justify-content-between align-items-center">
      <small class="text-muted d-flex align-items-center">
        <i class="fas fa-clock mr-1 text-info"></i>
        Gerado em {{dataGeracaoRelatorio}}
      </small>
      <div>
        <button class="btn btn-sm btn-outline-secondary mr-2" (click)="copiarRelatorio()" kendoTooltip title="Copiar para área de transferência">
          <i class="fas fa-copy mr-1"></i> Copiar
        </button>
        <button class="btn btn-sm btn-outline-danger" (click)="fecharRelatorio()">
          <i class="fas fa-times mr-1"></i> Fechar
        </button>
      </div>
    </div>
  </div>
</div>

<div class="mb-2" *ngIf="dev">
  <div class="form-group mt-2">
    <label>Enviar mensagem Teste Chatbot:</label>
    <input type="text" class="form-control" (keyup.enter)="digitouMensagemBot($event)"/>
  </div>
</div>

<div class="k-i-loading ml-1 mr-1 mt-3" style="font-size: 50px; padding-bottom: 20px" *ngIf="!carregou" ></div>

<div class="row"  [hidden]="contato == null">
  <div class="col-12">
    <info-contato [contato]="contato" [exibirUltimaVisita]="true" [atendente]="atendente" [statusMia]="statusMia"
                  [desativarParaSempre]="desativarParaSempre"
                  [inserirtTag]="true" [empresa]="empresa" [exibirEndereco]="true" [limiteEndereco]="2">
    </info-contato>
  </div>

  <div class="col-12 mt-2">
    <div class="alert alert-success mb-0" role="alert" *ngIf="msg">
      <i class="mdi mdi-check-all mr-2"></i> {{msg}}  <i class="fa fa-print fa-lg cpointer ml-1" *ngIf="novoPedido.guid"
                                                         (click)="imprimaPedido(this.novoPedido)"> </i>
    </div>
  </div>

  <div class="col-12 mb-3" *ngIf="contato  && (contato.telefone || contato.id)">
    <acoes-modulos [contato]="contato" (salvouContato)="salveContato($event)"></acoes-modulos>
  </div>

  <div class="col-12">
    <info-pedidos [contato]="contato"  [empresa]="empresa"></info-pedidos>
  </div>

  <div class="col-12 ">
    <info-fidelidade *ngIf="contato" [cartoes]="contato.cartoes" [empresa]="empresa"></info-fidelidade>
  </div>

  <div class="col-12">
    <app-historico-modulos [contato]="contato" [empresa]="empresa" ></app-historico-modulos>
  </div>
</div>

<div  [hidden]="!carregou || contato != null">
  <div class="row">
    <div class="col">
      <ng-container *ngIf="!empresa.dark">
        <h5 class="mt-2">Este é um novo cliente</h5>
      </ng-container>

      <info-contato [contato]="contatoPorDados" [atendente]="atendente" [statusMia]="statusMia"
                    [exibirUltimaVisita]="true" [empresa]="empresa" [desativarParaSempre]="desativarParaSempre"></info-contato>
      <acoes-modulos [contato]="contatoPorDados" [novoContato]="true" (salvouContato)="salveContato($event)"></acoes-modulos>
    </div>
  </div>
</div>
