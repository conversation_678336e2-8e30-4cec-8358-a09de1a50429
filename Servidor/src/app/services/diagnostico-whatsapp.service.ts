import { Injectable } from '@angular/core';
import { NotificationService } from './notification.service';

export interface LogDiagnostico {
  timestamp: Date;
  telefone: string;
  nome: string;
  tipo: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  categoria: 'PROCESSAMENTO' | 'VALIDACAO' | 'ENVIO' | 'SISTEMA';
  mensagem: string;
  textoMensagem?: string; // Texto da mensagem WhatsApp sendo processada
  detalhes?: any;
}

@Injectable({
  providedIn: 'root'
})
export class DiagnosticoWhatsappService {
  private logs: LogDiagnostico[] = [];
  private maxLogs = 100; // Manter apenas os últimos 100 logs
  private readonly storageKey = 'diagnostico_whatsapp_logs';

  constructor(private notificationService: NotificationService) {
    this.carregarLogsDoStorage();
  }

  /**
   * Adiciona um log de diagnóstico
   */
  adicionarLog(log: Omit<LogDiagnostico, 'timestamp'>): void {
    const novoLog: LogDiagnostico = {
      ...log,
      timestamp: new Date()
    };

    this.logs.unshift(novoLog);

    // Manter apenas os logs mais recentes
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Salvar no localStorage
    this.salvarLogsNoStorage();

    // Log no console para debug técnico
    const textoInfo = log.textoMensagem ? ` | Texto: "${log.textoMensagem.substring(0, 50)}${log.textoMensagem.length > 50 ? '...' : ''}"` : '';
    console.log(`[DiagnosticoWhatsapp] ${log.categoria} - ${log.tipo}: ${log.mensagem}${textoInfo}`, log.detalhes);
    console.log(`[DiagnosticoWhatsapp] Total de logs armazenados: ${this.logs.length}`);
  }

  /**
   * Logs específicos para processamento de mensagens
   */
  logProcessamentoMensagem(telefone: string, nome: string, mensagem: string, textoMensagem?: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'INFO',
      categoria: 'PROCESSAMENTO',
      mensagem,
      textoMensagem,
      detalhes
    });
  }

  logValidacaoFalhou(telefone: string, nome: string, motivo: string, textoMensagem?: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'WARNING',
      categoria: 'VALIDACAO',
      mensagem: `Validação falhou: ${motivo}`,
      textoMensagem,
      detalhes
    });
  }

  logEnvioSucesso(telefone: string, nome: string, textoMensagem?: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'SUCCESS',
      categoria: 'ENVIO',
      mensagem: 'Mensagem de saudação enviada com sucesso',
      textoMensagem,
      detalhes
    });
  }

  logEnvioFalhou(telefone: string, nome: string, motivo: string, textoMensagem?: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'ERROR',
      categoria: 'ENVIO',
      mensagem: `Envio falhou: ${motivo}`,
      textoMensagem,
      detalhes
    });
  }

  /**
   * Logs específicos para diferentes cenários
   */
  logSaudacaoEnviada(telefone: string, nome: string, textoMensagem?: string, detalhes?: any): void {
    this.logEnvioSucesso(telefone, nome, textoMensagem, detalhes);
  }

  logSaudacaoBloqueada(telefone: string, nome: string, motivo: string, textoMensagem?: string, detalhes?: any): void {
    this.logValidacaoFalhou(telefone, nome, motivo, textoMensagem, detalhes);
  }

  logTempoInsuficiente(telefone: string, nome: string, tempoDecorrido: number, tempoNecessario: number, textoMensagem?: string): void {
    this.logValidacaoFalhou(telefone, nome, 'Tempo insuficiente para nova saudação', textoMensagem, {
      tempoDecorrido: tempoDecorrido,
      tempoNecessario: tempoNecessario
    });
  }

  /**
   * Logs específicos para tipos de mensagem
   */
  logMensagemPromocional(telefone: string, nome: string, textoMensagem: string, sucesso: boolean, detalhes?: any): void {
    if (sucesso) {
      this.adicionarLog({
        telefone,
        nome,
        tipo: 'SUCCESS',
        categoria: 'ENVIO',
        mensagem: 'Mensagem promocional enviada com sucesso',
        textoMensagem,
        detalhes
      });
    } else {
      this.adicionarLog({
        telefone,
        nome,
        tipo: 'ERROR',
        categoria: 'ENVIO',
        mensagem: 'Falha ao enviar mensagem promocional',
        textoMensagem,
        detalhes
      });
    }
  }

  logMensagemAutomatica(telefone: string, nome: string, textoMensagem: string, tipo: 'saudacao' | 'resposta' | 'followup', sucesso: boolean, detalhes?: any): void {
    const tipoDescricao = {
      'saudacao': 'saudação',
      'resposta': 'resposta automática',
      'followup': 'follow-up'
    };

    if (sucesso) {
      this.adicionarLog({
        telefone,
        nome,
        tipo: 'SUCCESS',
        categoria: 'ENVIO',
        mensagem: `Mensagem de ${tipoDescricao[tipo]} enviada com sucesso`,
        textoMensagem,
        detalhes
      });
    } else {
      this.adicionarLog({
        telefone,
        nome,
        tipo: 'ERROR',
        categoria: 'ENVIO',
        mensagem: `Falha ao enviar mensagem de ${tipoDescricao[tipo]}`,
        textoMensagem,
        detalhes
      });
    }
  }

  /**
   * Obtém logs filtrados por telefone (busca flexível)
   */
  obterLogsPorTelefone(telefone: string): LogDiagnostico[] {
    console.log(`[DiagnosticoWhatsapp] Buscando logs para telefone: "${telefone}"`);
    console.log(`[DiagnosticoWhatsapp] Telefones nos logs:`, this.logs.map(log => log.telefone));

    // Busca exata primeiro
    let logsEncontrados = this.logs.filter(log => log.telefone === telefone);

    // Se não encontrou, tentar busca flexível
    if (logsEncontrados.length === 0) {
      // Remover caracteres especiais e espaços para comparação
      const telefoneNormalizado = telefone.replace(/[^\d]/g, '');

      logsEncontrados = this.logs.filter(log => {
        const logTelefoneNormalizado = log.telefone.replace(/[^\d]/g, '');
        return logTelefoneNormalizado.includes(telefoneNormalizado) ||
               telefoneNormalizado.includes(logTelefoneNormalizado);
      });

      console.log(`[DiagnosticoWhatsapp] Busca flexível com telefone normalizado "${telefoneNormalizado}":`, logsEncontrados.length);
    }

    console.log(`[DiagnosticoWhatsapp] Logs encontrados: ${logsEncontrados.length}`);

    return logsEncontrados;
  }

  /**
   * Obtém todos os logs
   */
  obterTodosLogs(): LogDiagnostico[] {
    return [...this.logs];
  }

  /**
   * Obtém logs recentes (últimos 10)
   */
  obterLogsRecentes(): LogDiagnostico[] {
    return this.logs.slice(0, 10);
  }

  /**
   * Limpa todos os logs
   */
  limparLogs(): void {
    this.logs = [];
    this.salvarLogsNoStorage();
    console.log('[DiagnosticoWhatsapp] Todos os logs foram limpos');
  }

  /**
   * Exibe notificação para o usuário sobre status do processamento
   */
  notificarUsuario(tipo: 'success' | 'error' | 'info', mensagem: string): void {
    switch (tipo) {
      case 'success':
        this.notificationService.showSuccess(mensagem);
        break;
      case 'error':
        this.notificationService.showError(mensagem);
        break;
      case 'info':
        // Para mensagens informativas, usar o método genérico do Kendo
        this.notificationService['notificationService'].show({
          content: mensagem,
          cssClass: 'info',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'info', icon: true },
          hideAfter: 4000
        });
        break;
    }
  }

  /**
   * Gera relatório de diagnóstico formatado
   */
  gerarRelatorio(telefone?: string): string {
    console.log(`[DiagnosticoWhatsapp] Gerando relatório para telefone: ${telefone}`);
    console.log(`[DiagnosticoWhatsapp] Total de logs no sistema: ${this.logs.length}`);
    console.log(`[DiagnosticoWhatsapp] Todos os logs:`, this.logs);

    const logsParaRelatorio = telefone ? this.obterLogsPorTelefone(telefone) : this.obterLogsRecentes();

    console.log(`[DiagnosticoWhatsapp] Logs para relatório: ${logsParaRelatorio.length}`);
    console.log(`[DiagnosticoWhatsapp] Logs filtrados:`, logsParaRelatorio);

    if (logsParaRelatorio.length === 0) {
      // Mostrar informações de debug quando não há logs
      let debugInfo = telefone
        ? `Nenhum log encontrado para o telefone ${telefone}.\n\n`
        : `Nenhum log de diagnóstico encontrado no sistema.\n\n`;

      debugInfo += `📊 DEBUG INFO:\n`;
      debugInfo += `• Total de logs no sistema: ${this.logs.length}\n`;

      if (this.logs.length > 0) {
        debugInfo += `• Telefones nos logs: ${this.logs.map(log => log.telefone).join(', ')}\n`;
        debugInfo += `• Últimos 3 logs:\n`;
        this.logs.slice(0, 3).forEach((log, index) => {
          debugInfo += `  ${index + 1}. [${log.timestamp.toLocaleTimeString()}] ${log.telefone}: ${log.mensagem}\n`;
        });
      }

      return debugInfo;
    }

    let relatorio = `📊 RELATÓRIO DE DIAGNÓSTICO WHATSAPP\n`;
    relatorio += `📅 Gerado em: ${new Date().toLocaleString('pt-BR')}\n`;

    if (telefone) {
      relatorio += `📞 Telefone: ${telefone}\n`;
    }

    relatorio += `📝 Total de logs: ${logsParaRelatorio.length}\n\n`;

    // Primeiro, mostrar todos os logs em ordem cronológica (mais recentes primeiro)
    relatorio += `📋 HISTÓRICO COMPLETO (mais recentes primeiro):\n`;
    logsParaRelatorio.forEach((log, index) => {
      const emoji = this.obterEmojiPorTipo(log.tipo);
      const tempo = log.timestamp.toLocaleTimeString('pt-BR');
      relatorio += `${emoji} [${tempo}] ${log.categoria}: ${log.mensagem}\n`;
    });

    relatorio += `\n`;

    // Depois, agrupar por categoria para análise
    const categorias = ['PROCESSAMENTO', 'VALIDACAO', 'ENVIO', 'SISTEMA'];

    relatorio += `📊 RESUMO POR CATEGORIA:\n`;
    categorias.forEach(categoria => {
      const logsDaCategoria = logsParaRelatorio.filter(log => log.categoria === categoria);

      if (logsDaCategoria.length > 0) {
        relatorio += `• ${categoria}: ${logsDaCategoria.length} logs\n`;
      }
    });

    return relatorio;
  }

  /**
   * Gera relatório visual melhorado
   */
  gerarRelatorioSimples(telefone?: string): string {
    const logs = telefone ? this.obterLogsPorTelefone(telefone) : this.obterTodosLogs();

    let relatorio = `🔍 LOGS WHATSAPP - ${new Date().toLocaleString('pt-BR')}\n`;
    relatorio += `📱 ${telefone || 'TODOS OS TELEFONES'} | 📊 ${logs.length} logs\n`;
    relatorio += `${'═'.repeat(65)}\n\n`;

    if (logs.length === 0) {
      relatorio += `❌ NENHUM LOG ENCONTRADO\n\n`;
      relatorio += `🔧 DEBUG INFO:\n`;
      relatorio += `• Total no sistema: ${this.logs.length} logs\n`;

      if (this.logs.length > 0) {
        const telefones = [...new Set(this.logs.map(l => l.telefone))];
        relatorio += `• Telefones: ${telefones.slice(0, 3).join(', ')}${telefones.length > 3 ? '...' : ''}\n`;
      }

      return relatorio;
    }

    // Agrupar logs por categoria para estatísticas
    const estatisticas = this.obterEstatisticasVisuais(logs);
    relatorio += `📈 RESUMO:\n`;
    relatorio += `${estatisticas}\n`;
    relatorio += `${'─'.repeat(65)}\n\n`;

    logs.forEach((log, i) => {
      const statusIcon = this.obterIconeStatus(log);
      const categoriaIcon = this.obterIconeCategoria(log.categoria);
      const tempo = log.timestamp.toLocaleTimeString('pt-BR');
      const tempoRelativo = this.obterTempoRelativo(log.timestamp);

      relatorio += `${statusIcon} ${categoriaIcon} ${log.categoria}\n`;
      relatorio += `⏰ ${tempo} (${tempoRelativo}) | 👤 ${log.nome || 'Sistema'}\n`;
      relatorio += `💬 ${log.mensagem}\n`;

      // Mostrar texto da mensagem WhatsApp se disponível
      if (log.textoMensagem) {
        const textoTruncado = log.textoMensagem.length > 100
          ? log.textoMensagem.substring(0, 100) + '...'
          : log.textoMensagem;
        relatorio += `📝 Texto: "${textoTruncado}"\n`;
      }

      // Mostrar detalhes importantes de forma mais visual
      if (log.detalhes && Object.keys(log.detalhes).length > 0) {
        const detalhesFormatados = this.formatarDetalhes(log.detalhes);
        if (detalhesFormatados) {
          relatorio += `${detalhesFormatados}\n`;
        }
      }

      if (i < logs.length - 1) {
        relatorio += `${'┄'.repeat(40)}\n`;
      }
      relatorio += `\n`;
    });

    return relatorio;
  }

  /**
   * Obtém ícone de status baseado no tipo e contexto do log
   */
  private obterIconeStatus(log: LogDiagnostico): string {
    // Analisar o contexto da mensagem para determinar sucesso/falha
    const mensagem = log.mensagem.toLowerCase();

    if (log.tipo === 'SUCCESS' || mensagem.includes('enviada com sucesso') || mensagem.includes('sucesso')) {
      return '✅'; // Sucesso
    }

    if (log.tipo === 'ERROR' || mensagem.includes('erro') || mensagem.includes('falhou')) {
      return '❌'; // Erro
    }

    if (log.tipo === 'WARNING' || mensagem.includes('bloqueada') || mensagem.includes('insuficiente') ||
        mensagem.includes('não vai enviar') || mensagem.includes('validação falhou')) {
      return '⚠️'; // Aviso/Bloqueado
    }

    if (mensagem.includes('processando') || mensagem.includes('verificando') || mensagem.includes('iniciando')) {
      return '🔄'; // Processando
    }

    return '🔵'; // Info padrão
  }

  /**
   * Obtém ícone da categoria
   */
  private obterIconeCategoria(categoria: string): string {
    const icones = {
      'ENVIO': '📤',
      'VALIDACAO': '🔍',
      'PROCESSAMENTO': '⚙️',
      'SISTEMA': '🖥️'
    };
    return icones[categoria] || '📋';
  }

  /**
   * Calcula tempo relativo (há X minutos)
   */
  private obterTempoRelativo(timestamp: Date): string {
    const agora = new Date();
    const diffMs = agora.getTime() - timestamp.getTime();
    const diffMinutos = Math.floor(diffMs / 60000);

    if (diffMinutos < 1) return 'agora';
    if (diffMinutos < 60) return `há ${diffMinutos}min`;

    const diffHoras = Math.floor(diffMinutos / 60);
    if (diffHoras < 24) return `há ${diffHoras}h`;

    const diffDias = Math.floor(diffHoras / 24);
    return `há ${diffDias}d`;
  }

  /**
   * Gera estatísticas visuais
   */
  private obterEstatisticasVisuais(logs: LogDiagnostico[]): string {
    let sucessos = 0, erros = 0, avisos = 0, processando = 0;

    logs.forEach(log => {
      const mensagem = log.mensagem.toLowerCase();

      if (log.tipo === 'SUCCESS' || mensagem.includes('enviada com sucesso')) {
        sucessos++;
      } else if (log.tipo === 'ERROR' || mensagem.includes('erro')) {
        erros++;
      } else if (log.tipo === 'WARNING' || mensagem.includes('bloqueada') || mensagem.includes('insuficiente')) {
        avisos++;
      } else {
        processando++;
      }
    });

    let stats = '';
    if (sucessos > 0) stats += `✅ ${sucessos} sucessos  `;
    if (erros > 0) stats += `❌ ${erros} erros  `;
    if (avisos > 0) stats += `⚠️ ${avisos} bloqueios  `;
    if (processando > 0) stats += `🔄 ${processando} processamentos`;

    return stats || '📊 Sem estatísticas';
  }

  /**
   * Formata detalhes de forma mais legível
   */
  private formatarDetalhes(detalhes: any): string {
    let resultado = '';

    // Detalhes específicos que são importantes mostrar
    if (detalhes.tempoDecorrido !== undefined && detalhes.tempoNecessario !== undefined) {
      resultado += `⏱️ Tempo: ${detalhes.tempoDecorrido.toFixed(1)}h / ${detalhes.tempoNecessario.toFixed(1)}h necessárias\n`;
    }

    if (detalhes.statusMia) {
      resultado += `🤖 Mia: ${detalhes.statusMia}\n`;
    }

    if (detalhes.mensagemId) {
      resultado += `🆔 ID: ${detalhes.mensagemId}\n`;
    }

    if (detalhes.horarioEnvio) {
      resultado += `📅 Enviado: ${detalhes.horarioEnvio}\n`;
    }

    return resultado;
  }

  private obterEmojiPorTipo(tipo: string): string {
    const emojis = {
      'INFO': '🔵',
      'WARNING': '🟡',
      'ERROR': '🔴',
      'SUCCESS': '🟢'
    };
    return emojis[tipo] || '🔵';
  }

  /**
   * Obtém estatísticas dos logs
   */
  obterEstatisticas(telefone?: string): any {
    const logs = telefone ? this.obterLogsPorTelefone(telefone) : this.obterTodosLogs();
    
    const stats = {
      total: logs.length,
      porTipo: {
        INFO: 0,
        WARNING: 0,
        ERROR: 0,
        SUCCESS: 0
      },
      porCategoria: {
        PROCESSAMENTO: 0,
        VALIDACAO: 0,
        ENVIO: 0,
        SISTEMA: 0
      },
      ultimoLog: logs[0]?.timestamp || null
    };

    logs.forEach(log => {
      stats.porTipo[log.tipo]++;
      stats.porCategoria[log.categoria]++;
    });

    return stats;
  }

  /**
   * Carrega logs do localStorage
   */
  private carregarLogsDoStorage(): void {
    try {
      const logsString = localStorage.getItem(this.storageKey);
      if (logsString) {
        const logsCarregados = JSON.parse(logsString);

        // Converter strings de timestamp de volta para Date objects
        this.logs = logsCarregados.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }));

        console.log(`[DiagnosticoWhatsapp] Carregados ${this.logs.length} logs do localStorage`);

        // Limpar logs muito antigos automaticamente
        this.limparLogsAntigos();
      }
    } catch (error) {
      console.error('[DiagnosticoWhatsapp] Erro ao carregar logs do localStorage:', error);
      this.logs = [];
      // Tentar limpar localStorage corrompido
      try {
        localStorage.removeItem(this.storageKey);
      } catch (e) {
        console.error('[DiagnosticoWhatsapp] Erro ao limpar localStorage corrompido:', e);
      }
    }
  }

  /**
   * Salva logs no localStorage
   */
  private salvarLogsNoStorage(): void {
    try {
      const logsString = JSON.stringify(this.logs);
      localStorage.setItem(this.storageKey, logsString);
    } catch (error) {
      console.error('[DiagnosticoWhatsapp] Erro ao salvar logs no localStorage:', error);

      // Se der erro (provavelmente por falta de espaço), limpar logs antigos
      if (error.name === 'QuotaExceededError') {
        console.warn('[DiagnosticoWhatsapp] Quota excedida, reduzindo número de logs...');
        this.logs = this.logs.slice(0, Math.floor(this.maxLogs / 2));
        this.salvarLogsNoStorage(); // Tentar salvar novamente com menos logs
      }
    }
  }

  /**
   * Limpa logs antigos (mais de 7 dias)
   */
  limparLogsAntigos(): void {
    const seteDiasAtras = new Date();
    seteDiasAtras.setDate(seteDiasAtras.getDate() - 7);

    const logsRecentes = this.logs.filter(log => log.timestamp > seteDiasAtras);

    if (logsRecentes.length !== this.logs.length) {
      console.log(`[DiagnosticoWhatsapp] Removendo ${this.logs.length - logsRecentes.length} logs antigos`);
      this.logs = logsRecentes;
      this.salvarLogsNoStorage();
    }
  }

  /**
   * Limpa TODOS os logs (incluindo localStorage) - método público
   */
  limparTodosLogsCompleto(): void {
    this.logs = [];
    try {
      localStorage.removeItem(this.storageKey);
      console.log('[DiagnosticoWhatsapp] Todos os logs foram completamente removidos');
    } catch (error) {
      console.error('[DiagnosticoWhatsapp] Erro ao limpar localStorage:', error);
    }
  }

  /**
   * Obtém informações sobre o armazenamento
   */
  obterInfoArmazenamento(): any {
    try {
      const logsString = localStorage.getItem(this.storageKey);
      const tamanhoBytes = logsString ? new Blob([logsString]).size : 0;
      const tamanhoKB = Math.round(tamanhoBytes / 1024 * 100) / 100;

      // Calcular idade dos logs
      const agora = new Date();
      const logMaisAntigo = this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null;
      const diasArmazenados = logMaisAntigo ?
        Math.floor((agora.getTime() - logMaisAntigo.getTime()) / (1000 * 60 * 60 * 24)) : 0;

      return {
        totalLogs: this.logs.length,
        tamanhoKB: tamanhoKB,
        tamanhoBytes: tamanhoBytes,
        maxLogs: this.maxLogs,
        logMaisAntigo: logMaisAntigo,
        logMaisRecente: this.logs.length > 0 ? this.logs[0].timestamp : null,
        diasArmazenados: diasArmazenados,
        retencaoDias: 7,
        percentualUso: Math.round((this.logs.length / this.maxLogs) * 100),
        storageKey: this.storageKey
      };
    } catch (error) {
      return {
        erro: 'Erro ao obter informações de armazenamento',
        detalhes: error.message
      };
    }
  }

  /**
   * Gera relatório sobre o armazenamento de logs
   */
  gerarRelatorioArmazenamento(): string {
    const info = this.obterInfoArmazenamento();

    if (info.erro) {
      return `❌ ERRO NO ARMAZENAMENTO\n${info.detalhes}`;
    }

    let relatorio = `💾 INFORMAÇÕES DE ARMAZENAMENTO\n`;
    relatorio += `${'═'.repeat(50)}\n\n`;

    relatorio += `📊 ESTATÍSTICAS:\n`;
    relatorio += `• Total de logs: ${info.totalLogs}/${info.maxLogs} (${info.percentualUso}%)\n`;
    relatorio += `• Tamanho: ${info.tamanhoKB} KB (${info.tamanhoBytes} bytes)\n`;
    relatorio += `• Retenção: ${info.retencaoDias} dias\n`;
    relatorio += `• Dias armazenados: ${info.diasArmazenados}\n\n`;

    relatorio += `📅 PERÍODO:\n`;
    if (info.logMaisRecente) {
      relatorio += `• Mais recente: ${info.logMaisRecente.toLocaleString('pt-BR')}\n`;
    }
    if (info.logMaisAntigo) {
      relatorio += `• Mais antigo: ${info.logMaisAntigo.toLocaleString('pt-BR')}\n`;
    }

    if (info.totalLogs === 0) {
      relatorio += `\n⚠️ Nenhum log armazenado\n`;
    }

    relatorio += `\n🔧 TÉCNICO:\n`;
    relatorio += `• Chave localStorage: ${info.storageKey}\n`;
    relatorio += `• Limpeza automática: Logs > 7 dias\n`;
    relatorio += `• Limite máximo: ${info.maxLogs} logs\n`;

    return relatorio;
  }
}
