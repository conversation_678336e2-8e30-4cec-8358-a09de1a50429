import { Injectable } from '@angular/core';
import { NotificationService } from './notification.service';

export interface LogDiagnostico {
  timestamp: Date;
  telefone: string;
  nome: string;
  tipo: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS';
  categoria: 'PROCESSAMENTO' | 'VALIDACAO' | 'ENVIO' | 'SISTEMA';
  mensagem: string;
  detalhes?: any;
}

@Injectable({
  providedIn: 'root'
})
export class DiagnosticoWhatsappService {
  private logs: LogDiagnostico[] = [];
  private maxLogs = 100; // Manter apenas os últimos 100 logs
  private readonly storageKey = 'diagnostico_whatsapp_logs';

  constructor(private notificationService: NotificationService) {
    this.carregarLogsDoStorage();
  }

  /**
   * Adiciona um log de diagnóstico
   */
  adicionarLog(log: Omit<LogDiagnostico, 'timestamp'>): void {
    const novoLog: LogDiagnostico = {
      ...log,
      timestamp: new Date()
    };

    this.logs.unshift(novoLog);

    // Manter apenas os logs mais recentes
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Salvar no localStorage
    this.salvarLogsNoStorage();

    // Log no console para debug técnico
    console.log(`[DiagnosticoWhatsapp] ${log.categoria} - ${log.tipo}: ${log.mensagem}`, log.detalhes);
    console.log(`[DiagnosticoWhatsapp] Total de logs armazenados: ${this.logs.length}`);
  }

  /**
   * Logs específicos para processamento de mensagens
   */
  logProcessamentoMensagem(telefone: string, nome: string, mensagem: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'INFO',
      categoria: 'PROCESSAMENTO',
      mensagem,
      detalhes
    });
  }

  logValidacaoFalhou(telefone: string, nome: string, motivo: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'WARNING',
      categoria: 'VALIDACAO',
      mensagem: `Validação falhou: ${motivo}`,
      detalhes
    });
  }

  logEnvioSucesso(telefone: string, nome: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'SUCCESS',
      categoria: 'ENVIO',
      mensagem: 'Mensagem de saudação enviada com sucesso',
      detalhes
    });
  }

  logEnvioFalhou(telefone: string, nome: string, motivo: string, detalhes?: any): void {
    this.adicionarLog({
      telefone,
      nome,
      tipo: 'ERROR',
      categoria: 'ENVIO',
      mensagem: `Envio falhou: ${motivo}`,
      detalhes
    });
  }

  /**
   * Obtém logs filtrados por telefone (busca flexível)
   */
  obterLogsPorTelefone(telefone: string): LogDiagnostico[] {
    console.log(`[DiagnosticoWhatsapp] Buscando logs para telefone: "${telefone}"`);
    console.log(`[DiagnosticoWhatsapp] Telefones nos logs:`, this.logs.map(log => log.telefone));

    // Busca exata primeiro
    let logsEncontrados = this.logs.filter(log => log.telefone === telefone);

    // Se não encontrou, tentar busca flexível
    if (logsEncontrados.length === 0) {
      // Remover caracteres especiais e espaços para comparação
      const telefoneNormalizado = telefone.replace(/[^\d]/g, '');

      logsEncontrados = this.logs.filter(log => {
        const logTelefoneNormalizado = log.telefone.replace(/[^\d]/g, '');
        return logTelefoneNormalizado.includes(telefoneNormalizado) ||
               telefoneNormalizado.includes(logTelefoneNormalizado);
      });

      console.log(`[DiagnosticoWhatsapp] Busca flexível com telefone normalizado "${telefoneNormalizado}":`, logsEncontrados.length);
    }

    console.log(`[DiagnosticoWhatsapp] Logs encontrados: ${logsEncontrados.length}`);

    return logsEncontrados;
  }

  /**
   * Obtém todos os logs
   */
  obterTodosLogs(): LogDiagnostico[] {
    return [...this.logs];
  }

  /**
   * Obtém logs recentes (últimos 10)
   */
  obterLogsRecentes(): LogDiagnostico[] {
    return this.logs.slice(0, 10);
  }

  /**
   * Limpa todos os logs
   */
  limparLogs(): void {
    this.logs = [];
    this.salvarLogsNoStorage();
  }

  /**
   * Exibe notificação para o usuário sobre status do processamento
   */
  notificarUsuario(tipo: 'success' | 'error' | 'info', mensagem: string): void {
    switch (tipo) {
      case 'success':
        this.notificationService.showSuccess(mensagem);
        break;
      case 'error':
        this.notificationService.showError(mensagem);
        break;
      case 'info':
        // Para mensagens informativas, usar o método genérico do Kendo
        this.notificationService['notificationService'].show({
          content: mensagem,
          cssClass: 'info',
          animation: { type: 'slide', duration: 400 },
          position: { horizontal: 'right', vertical: 'top' },
          type: { style: 'info', icon: true },
          hideAfter: 4000
        });
        break;
    }
  }

  /**
   * Gera relatório de diagnóstico formatado
   */
  gerarRelatorio(telefone?: string): string {
    console.log(`[DiagnosticoWhatsapp] Gerando relatório para telefone: ${telefone}`);
    console.log(`[DiagnosticoWhatsapp] Total de logs no sistema: ${this.logs.length}`);
    console.log(`[DiagnosticoWhatsapp] Todos os logs:`, this.logs);

    const logsParaRelatorio = telefone ? this.obterLogsPorTelefone(telefone) : this.obterLogsRecentes();

    console.log(`[DiagnosticoWhatsapp] Logs para relatório: ${logsParaRelatorio.length}`);
    console.log(`[DiagnosticoWhatsapp] Logs filtrados:`, logsParaRelatorio);

    if (logsParaRelatorio.length === 0) {
      // Mostrar informações de debug quando não há logs
      let debugInfo = telefone
        ? `Nenhum log encontrado para o telefone ${telefone}.\n\n`
        : `Nenhum log de diagnóstico encontrado no sistema.\n\n`;

      debugInfo += `📊 DEBUG INFO:\n`;
      debugInfo += `• Total de logs no sistema: ${this.logs.length}\n`;

      if (this.logs.length > 0) {
        debugInfo += `• Telefones nos logs: ${this.logs.map(log => log.telefone).join(', ')}\n`;
        debugInfo += `• Últimos 3 logs:\n`;
        this.logs.slice(0, 3).forEach((log, index) => {
          debugInfo += `  ${index + 1}. [${log.timestamp.toLocaleTimeString()}] ${log.telefone}: ${log.mensagem}\n`;
        });
      }

      return debugInfo;
    }

    let relatorio = `📊 RELATÓRIO DE DIAGNÓSTICO WHATSAPP\n`;
    relatorio += `📅 Gerado em: ${new Date().toLocaleString('pt-BR')}\n`;

    if (telefone) {
      relatorio += `📞 Telefone: ${telefone}\n`;
    }

    relatorio += `📝 Total de logs: ${logsParaRelatorio.length}\n\n`;

    // Primeiro, mostrar todos os logs em ordem cronológica (mais recentes primeiro)
    relatorio += `📋 HISTÓRICO COMPLETO (mais recentes primeiro):\n`;
    logsParaRelatorio.forEach((log, index) => {
      const emoji = this.obterEmojiPorTipo(log.tipo);
      const tempo = log.timestamp.toLocaleTimeString('pt-BR');
      relatorio += `${emoji} [${tempo}] ${log.categoria}: ${log.mensagem}\n`;
    });

    relatorio += `\n`;

    // Depois, agrupar por categoria para análise
    const categorias = ['PROCESSAMENTO', 'VALIDACAO', 'ENVIO', 'SISTEMA'];

    relatorio += `📊 RESUMO POR CATEGORIA:\n`;
    categorias.forEach(categoria => {
      const logsDaCategoria = logsParaRelatorio.filter(log => log.categoria === categoria);

      if (logsDaCategoria.length > 0) {
        relatorio += `• ${categoria}: ${logsDaCategoria.length} logs\n`;
      }
    });

    return relatorio;
  }

  /**
   * Gera relatório simples para debug
   */
  gerarRelatorioSimples(telefone?: string): string {
    const logs = telefone ? this.obterLogsPorTelefone(telefone) : this.obterTodosLogs();

    let relatorio = `🔍 RELATÓRIO SIMPLES - ${new Date().toLocaleString('pt-BR')}\n`;
    relatorio += `📱 Telefone: ${telefone || 'TODOS'}\n`;
    relatorio += `📊 Total: ${logs.length} logs\n\n`;

    if (logs.length === 0) {
      relatorio += `❌ Nenhum log encontrado!\n\n`;
      relatorio += `🔧 DEBUG:\n`;
      relatorio += `• Logs totais no sistema: ${this.logs.length}\n`;
      if (this.logs.length > 0) {
        relatorio += `• Telefones disponíveis: ${[...new Set(this.logs.map(l => l.telefone))].join(', ')}\n`;
      }
      return relatorio;
    }

    logs.forEach((log, i) => {
      const emoji = this.obterEmojiPorTipo(log.tipo);
      relatorio += `${i + 1}. ${emoji} [${log.timestamp.toLocaleTimeString()}]\n`;
      relatorio += `   📂 ${log.categoria} | 👤 ${log.nome}\n`;
      relatorio += `   💬 ${log.mensagem}\n\n`;
    });

    return relatorio;
  }

  private obterEmojiPorTipo(tipo: string): string {
    const emojis = {
      'INFO': 'ℹ️',
      'WARNING': '⚠️',
      'ERROR': '❌',
      'SUCCESS': '✅'
    };
    return emojis[tipo] || 'ℹ️';
  }

  /**
   * Obtém estatísticas dos logs
   */
  obterEstatisticas(telefone?: string): any {
    const logs = telefone ? this.obterLogsPorTelefone(telefone) : this.obterTodosLogs();
    
    const stats = {
      total: logs.length,
      porTipo: {
        INFO: 0,
        WARNING: 0,
        ERROR: 0,
        SUCCESS: 0
      },
      porCategoria: {
        PROCESSAMENTO: 0,
        VALIDACAO: 0,
        ENVIO: 0,
        SISTEMA: 0
      },
      ultimoLog: logs[0]?.timestamp || null
    };

    logs.forEach(log => {
      stats.porTipo[log.tipo]++;
      stats.porCategoria[log.categoria]++;
    });

    return stats;
  }

  /**
   * Carrega logs do localStorage
   */
  private carregarLogsDoStorage(): void {
    try {
      const logsString = localStorage.getItem(this.storageKey);
      if (logsString) {
        const logsCarregados = JSON.parse(logsString);

        // Converter strings de timestamp de volta para Date objects
        this.logs = logsCarregados.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }));

        console.log(`[DiagnosticoWhatsapp] Carregados ${this.logs.length} logs do localStorage`);
      }
    } catch (error) {
      console.error('[DiagnosticoWhatsapp] Erro ao carregar logs do localStorage:', error);
      this.logs = [];
    }
  }

  /**
   * Salva logs no localStorage
   */
  private salvarLogsNoStorage(): void {
    try {
      const logsString = JSON.stringify(this.logs);
      localStorage.setItem(this.storageKey, logsString);
    } catch (error) {
      console.error('[DiagnosticoWhatsapp] Erro ao salvar logs no localStorage:', error);

      // Se der erro (provavelmente por falta de espaço), limpar logs antigos
      if (error.name === 'QuotaExceededError') {
        console.warn('[DiagnosticoWhatsapp] Quota excedida, reduzindo número de logs...');
        this.logs = this.logs.slice(0, Math.floor(this.maxLogs / 2));
        this.salvarLogsNoStorage(); // Tentar salvar novamente com menos logs
      }
    }
  }

  /**
   * Limpa logs antigos (mais de 7 dias)
   */
  limparLogsAntigos(): void {
    const seteDiasAtras = new Date();
    seteDiasAtras.setDate(seteDiasAtras.getDate() - 7);

    const logsRecentes = this.logs.filter(log => log.timestamp > seteDiasAtras);

    if (logsRecentes.length !== this.logs.length) {
      console.log(`[DiagnosticoWhatsapp] Removendo ${this.logs.length - logsRecentes.length} logs antigos`);
      this.logs = logsRecentes;
      this.salvarLogsNoStorage();
    }
  }

  /**
   * Obtém informações sobre o armazenamento
   */
  obterInfoArmazenamento(): any {
    try {
      const logsString = localStorage.getItem(this.storageKey);
      const tamanhoBytes = logsString ? new Blob([logsString]).size : 0;
      const tamanhoKB = Math.round(tamanhoBytes / 1024 * 100) / 100;

      return {
        totalLogs: this.logs.length,
        tamanhoKB: tamanhoKB,
        tamanhoBytes: tamanhoBytes,
        maxLogs: this.maxLogs,
        logMaisAntigo: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null,
        logMaisRecente: this.logs.length > 0 ? this.logs[0].timestamp : null
      };
    } catch (error) {
      return {
        erro: 'Erro ao obter informações de armazenamento',
        detalhes: error.message
      };
    }
  }
}
