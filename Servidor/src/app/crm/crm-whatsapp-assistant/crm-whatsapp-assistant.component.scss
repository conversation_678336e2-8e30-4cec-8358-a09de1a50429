// WhatsApp Assistant - Design Limpo e Funcional
// Estilos simples e profissionais

// ===== ESTILOS GERAIS =====
.container-fluid {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 20px;
}

// ===== CARDS MODERNOS =====
.card {
  border: none !important;
  border-radius: 15px !important;
  box-shadow: var(--card-shadow) !important;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    box-shadow: var(--card-shadow-hover) !important;
    transform: translateY(-2px);
  }
  
  .card-header {
    border: none !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 20px !important;
    
    &.bg-primary {
      background: var(--primary-gradient) !important;
    }
    
    &.bg-success {
      background: var(--success-gradient) !important;
    }
    
    &.bg-warning {
      background: var(--warning-gradient) !important;
    }
    
    &.bg-info {
      background: var(--info-gradient) !important;
    }
    
    &.bg-danger {
      background: var(--danger-gradient) !important;
    }
    
    &.bg-secondary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    
    h5, h6 {
      margin: 0 !important;
      font-weight: 700 !important;
    }
  }
  
  .card-body {
    padding: 25px !important;
    
    &.bg-light {
      background: rgba(248, 249, 250, 0.8) !important;
    }
  }
}

// ===== AVATAR CIRCLE =====
.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

// ===== STATUS CARDS =====
.status-card {
  border-radius: 12px !important;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  i {
    opacity: 0.9;
  }
  
  h6 {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
  }
}

// ===== BADGES MODERNOS =====
.badge {
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  
  &.fs-6 {
    font-size: 14px !important;
    padding: 10px 16px !important;
  }
}

// ===== BOTÕES SPIN =====
.btn-group {
  .btn {
    border-radius: 8px !important;
    margin: 2px !important;
    padding: 12px 8px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    i {
      font-size: 18px;
      margin-bottom: 4px;
    }
    
    small {
      font-size: 10px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// ===== ESTADOS DOS BOTÕES SPIN =====
.badge-primary, .btn-primary {
  background: var(--primary-gradient) !important;
  border-color: transparent !important;
  color: white !important;
  
  &:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%) !important;
  }
}

.badge-light, .btn-outline-primary {
  background: white !important;
  border: 2px solid #e2e8f0 !important;
  color: #4a5568 !important;
  
  &:hover {
    border-color: #667eea !important;
    color: #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
  }
}

// ===== FORM CONTROLS =====
.form-control, .form-select {
  border-radius: 8px !important;
  border: 2px solid #e2e8f0 !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  
  &:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  }
  
  &.form-control-lg {
    padding: 16px 20px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
}

// ===== BOTÕES PRINCIPAIS =====
.btn {
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  
  &.btn-lg {
    padding: 16px 32px !important;
    font-size: 16px !important;
    border-radius: 12px !important;
  }
  
  &.btn-primary {
    background: var(--primary-gradient) !important;
    border: none !important;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
  }
  
  &.btn-success {
    background: var(--success-gradient) !important;
    border: none !important;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    }
  }
  
  &.btn-warning {
    background: var(--warning-gradient) !important;
    border: none !important;
    color: white !important;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
    }
  }
  
  &.btn-danger {
    background: var(--danger-gradient) !important;
    border: none !important;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 154, 158, 0.3);
    }
  }
  
  &:disabled {
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

// ===== ALERTAS MODERNOS =====
.alert {
  border: none !important;
  border-radius: 12px !important;
  padding: 16px 20px !important;
  
  &.alert-info {
    background: linear-gradient(135deg, rgba(168, 237, 234, 0.2) 0%, rgba(254, 214, 227, 0.2) 100%) !important;
    color: #2d3748 !important;
    border-left: 4px solid #4facfe !important;
  }
  
  &.alert-success {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.2) 0%, rgba(0, 242, 254, 0.2) 100%) !important;
    color: #2d3748 !important;
    border-left: 4px solid #00f2fe !important;
  }
  
  &.alert-warning {
    background: linear-gradient(135deg, rgba(250, 112, 154, 0.2) 0%, rgba(254, 225, 64, 0.2) 100%) !important;
    color: #2d3748 !important;
    border-left: 4px solid #fee140 !important;
  }
  
  &.alert-danger {
    background: linear-gradient(135deg, rgba(255, 154, 158, 0.2) 0%, rgba(254, 207, 239, 0.2) 100%) !important;
    color: #2d3748 !important;
    border-left: 4px solid #fecfef !important;
  }
  
  &.alert-primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%) !important;
    color: #2d3748 !important;
    border-left: 4px solid #764ba2 !important;
  }
}

// ===== MENSAGENS =====
.messages-container {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
  border-radius: 12px !important;
  
  .message-item {
    border-radius: 8px !important;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateX(2px);
    }
    
    &.bg-primary {
      background: var(--primary-gradient) !important;
    }
  }
}

// ===== FORM SWITCHES =====
.form-check-input {
  &:checked {
    background-color: #667eea !important;
    border-color: #667eea !important;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25) !important;
  }
}

// ===== ANIMAÇÕES =====
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotating 1s linear infinite;
}

// ===== RESPONSIVIDADE =====
@media (max-width: 768px) {
  .container-fluid {
    padding: 10px;
  }
  
  .card {
    margin-bottom: 15px !important;
  }
  
  .status-card {
    margin-bottom: 10px;
  }
  
  .btn-group .btn {
    font-size: 12px !important;
    padding: 8px 6px !important;
  }
}

// ===== UTILITÁRIOS =====
.shadow-sm {
  box-shadow: var(--card-shadow) !important;
}

.text-gradient-primary {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-0 {
  border: none !important;
}
