<!-- WhatsApp Assistant 2.0 - Layout com Cards -->
<div class="whatsapp-assistant-container">

  <!-- Loading Global -->
  <div *ngIf="carregandoDados" class="text-center py-5">
    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
      <span class="sr-only">Carregando...</span>
    </div>
    <h5 class="text-muted">Carregando dados do contato...</h5>
  </div>

  <!-- <PERSON><PERSON><PERSON><PERSON> Principal -->
  <div *ngIf="!carregandoDados" class="assistant-content">

    <!-- Card 1: Status do Lead -->
    <div class="card mb-3 border-0 shadow-sm">
      <div class="card-header bg-primary text-white">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center">
            <i class="fa-solid fa-user-circle me-2 fs-5"></i>
            <div>
              <h6 class="mb-0">{{ contato.nome || nomeWhatsApp || 'Novo Contato' }}</h6>
              <small class="opacity-75">{{ contato.empresa || 'Empresa não informada' }}</small>
            </div>
          </div>
          <div class="text-end">
            <div class="badge bg-warning text-dark mb-1">
              <i class="fa-solid fa-star me-1"></i>
              Score: 85%
            </div>
            <div class="small">
              <i class="fa-solid fa-circle text-success me-1"></i>
              Online agora
            </div>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <div class="text-center p-2 bg-light rounded">
              <i class="fa-solid fa-target text-primary fs-4"></i>
              <div class="small mt-1">
                <strong>Fase Atual</strong><br>
                <span class="text-primary">{{ getFaseDisplayName(faseSpin) }}</span>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="text-center p-2 bg-light rounded">
              <i class="fa-solid fa-clock text-success fs-4"></i>
              <div class="small mt-1">
                <strong>Tempo Resposta</strong><br>
                <span class="text-success">2 minutos</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Tags do Lead -->
        <div class="mt-3">
          <span class="badge bg-danger me-1">🔥 Hot Lead</span>
          <span class="badge bg-success me-1">💰 Alto Valor</span>
          <span class="badge bg-info">🍕 Pizzaria</span>
        </div>

        <!-- Concorrente (se houver) -->
        <div *ngIf="contato.sistemaConcorrente" class="alert alert-warning mt-3 mb-0">
          <div class="d-flex align-items-center">
            <i class="fa-solid fa-triangle-exclamation me-2"></i>
            <div>
              <strong>Usa {{ contato.sistemaConcorrente }}</strong><br>
              <small>Oportunidade: Economia R$ 1.500/mês</small>
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- 2. PERFIL SMART (Colapsável) -->
    <div class="profile-section">
      <div class="section-header" (click)="toggleSection('profile')">
        <h6 class="section-title">
          <i class="fa-solid fa-user"></i>
          Perfil do Lead
          <span *ngIf="!contato.id" class="new-badge">NOVO</span>
        </h6>
        <i class="fa-solid fa-chevron-down toggle-icon"
           [ngClass]="{'rotated': !sectionsCollapsed.profile}"></i>
      </div>

      <div class="section-content" [ngClass]="{'collapsed': sectionsCollapsed.profile}">
        <div class="profile-info">
          <div class="profile-main">
            <h6 class="company-name">
              <i class="fa-solid fa-building"></i>
              {{ contato.empresa || 'Empresa não informada' }}
            </h6>
            <p class="contact-details">
              <i class="fa-solid fa-phone"></i>
              {{ contato.telefone }}
            </p>
            <p class="contact-details" *ngIf="contato.email">
              <i class="fa-solid fa-envelope"></i>
              {{ contato.email }}
            </p>
            <p class="contact-details">
              <i class="fa-solid fa-location-dot"></i>
              São Paulo, SP
            </p>
          </div>

          <div class="profile-tags">
            <span class="tag hot">🔥 Hot</span>
            <span class="tag budget">💰 Alto</span>
            <span class="tag segment">🍕 Pizzaria</span>
          </div>

          <div class="competitor-info" *ngIf="contato.sistemaConcorrente">
            <div class="competitor-alert">
              <i class="fa-solid fa-triangle-exclamation"></i>
              <strong>CONCORRENTE:</strong> {{ contato.sistemaConcorrente }}
            </div>
            <div class="opportunity">
              <i class="fa-solid fa-lightbulb"></i>
              <strong>OPORTUNIDADE:</strong> Economia R$ 1.500/mês
            </div>
          </div>

          <div class="instagram-info" *ngIf="contato.instagramHandle">
            <a [href]="'https://instagram.com/' + contato.instagramHandle"
               target="_blank" class="instagram-link">
              <i class="fa-brands fa-instagram"></i>
              {{ '@' + contato.instagramHandle }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 3. IA ASSISTANT (Seção Principal) -->
    <div class="ai-assistant-section">
      <div class="section-header">
        <h6 class="section-title">
          <i class="fa-solid fa-robot"></i>
          Assistente IA
        </h6>
      </div>

      <div class="section-content">
        <!-- Fase Detectada -->
        <div class="phase-detection">
          <div class="current-phase-info">
            <span class="phase-label">🎯 FASE DETECTADA:</span>
            <span class="phase-name">{{ getFaseDisplayName(faseSpin).toUpperCase() }}</span>
          </div>
          <div class="next-phase" *ngIf="getNextPhase(faseSpin)">
            <span class="next-label">Próxima sugerida:</span>
            <span class="next-name">{{ getNextPhase(faseSpin) }}</span>
          </div>
        </div>

        <!-- Configuração Rápida -->
        <div class="quick-config">
          <div class="mode-toggles">
            <label class="toggle-option">
              <input type="checkbox" [(ngModel)]="modoRapport" (change)="toggleModoRapport()">
              <span class="toggle-label">
                <i class="fa-solid fa-heart"></i>
                Rapport/Atratividade
              </span>
            </label>
            <label class="toggle-option">
              <input type="checkbox" [(ngModel)]="modoApresentacao" (change)="toggleModoApresentacao()">
              <span class="toggle-label">
                <i class="fa-solid fa-user-check"></i>
                Apresentação Profissional
              </span>
            </label>
          </div>

          <!-- Fase SPIN (modo normal) -->
          <div class="spin-phases" *ngIf="!modoRapport && !modoApresentacao">
            <label class="config-label">FASE DA VENDA (SPIN SELLING):</label>
            <div class="phase-buttons">
              <button class="phase-btn"
                      [ngClass]="{'active': faseSpin === 'auto'}"
                      (click)="mudarFaseSpin('auto')">
                <i class="fa-solid fa-cpu"></i>
                Auto-detectar
              </button>
              <button class="phase-btn"
                      [ngClass]="{'active': faseSpin === 'situacao'}"
                      (click)="mudarFaseSpin('situacao')">
                <i class="fa-solid fa-circle-question"></i>
                Situação
              </button>
              <button class="phase-btn"
                      [ngClass]="{'active': faseSpin === 'problema'}"
                      (click)="mudarFaseSpin('problema')">
                <i class="fa-solid fa-triangle-exclamation"></i>
                Problema
              </button>
              <button class="phase-btn"
                      [ngClass]="{'active': faseSpin === 'implicacao'}"
                      (click)="mudarFaseSpin('implicacao')">
                <i class="fa-solid fa-arrow-trend-down"></i>
                Implicação
              </button>
              <button class="phase-btn"
                      [ngClass]="{'active': faseSpin === 'necessidade'}"
                      (click)="mudarFaseSpin('necessidade')">
                <i class="fa-solid fa-circle-check"></i>
                Necessidade
              </button>
            </div>

            <div class="tone-selector">
              <label class="config-label">TOM:</label>
              <select class="tone-select" [(ngModel)]="tipoTom">
                <option value="formal">Formal</option>
                <option value="informal">Informal</option>
                <option value="tecnico">Técnico</option>
              </select>
            </div>
          </div>

          <!-- Tipo de Abordagem (modo rapport) -->
          <div class="approach-types" *ngIf="modoRapport">
            <label class="config-label">TIPO DE ABORDAGEM OUTBOUND:</label>
            <div class="approach-buttons">
              <button class="approach-btn"
                      [ngClass]="{'active': tipoAbordagem === 'direta'}"
                      (click)="mudarTipoAbordagem('direta')">
                <i class="fa-solid fa-arrow-right"></i>
                Direta
              </button>
              <button class="approach-btn"
                      [ngClass]="{'active': tipoAbordagem === 'indireta'}"
                      (click)="mudarTipoAbordagem('indireta')">
                <i class="fa-solid fa-face-smile"></i>
                Indireta
              </button>
              <button class="approach-btn"
                      [ngClass]="{'active': tipoAbordagem === 'consultiva'}"
                      (click)="mudarTipoAbordagem('consultiva')">
                <i class="fa-solid fa-users"></i>
                Consultiva
              </button>
            </div>
          </div>
        </div>


        <!-- Botão de Gerar -->
        <div class="generate-section">
          <button class="generate-btn"
                  [disabled]="carregando"
                  (click)="gerarSugestao()">
            <span *ngIf="!carregando && !modoRapport && !modoApresentacao">
              <i class="fa-solid fa-cpu"></i>
              Gerar Sugestão de Resposta
            </span>
            <span *ngIf="!carregando && modoRapport">
              <i class="fa-solid fa-heart"></i>
              Gerar Mensagem de Rapport
            </span>
            <span *ngIf="!carregando && modoApresentacao">
              <i class="fa-solid fa-user-check"></i>
              Gerar Apresentação Profissional
            </span>
            <span *ngIf="carregando">
              <i class="fa-solid fa-spinner fa-spin"></i>
              {{ getLoadingText() }}
            </span>
          </button>

          <div class="mode-info" *ngIf="modoRapport">
            <small>
              <i class="fa-solid fa-info-circle"></i>
              Mensagens otimizadas para trabalho outbound e engajamento inicial
            </small>
          </div>
          <div class="mode-info" *ngIf="modoApresentacao">
            <small>
              <i class="fa-solid fa-info-circle"></i>
              Mensagem de apresentação profissional para primeiro contato
            </small>
          </div>
        </div>

        <!-- Erro -->
        <div *ngIf="erro" class="error-message">
          <i class="fa-solid fa-triangle-exclamation"></i>
          {{ erro }}
        </div>

        <!-- Sugestão Contextual -->
        <div *ngIf="sugestoes.length > 0 && !carregando" class="suggestion-result">
          <div class="suggestion-header">
            <span class="suggestion-label">💡 SUGESTÃO CONTEXTUAL:</span>
            <span class="suggestion-count">{{ sugestoes.length }} opções</span>
          </div>

          <div class="suggestion-content">
            <div class="suggestion-text">
              <textarea class="suggestion-textarea"
                        [value]="getSugestaoTexto()"
                        (input)="updateSugestaoTexto($event)"
                        rows="4"
                        placeholder="Sugestão aparecerá aqui..."></textarea>
            </div>

            <div class="suggestion-actions">
              <button class="action-btn primary" (click)="copiarSugestao(sugestaoSelecionada)">
                <i class="fa-solid fa-copy"></i>
                Copiar
              </button>
              <button class="action-btn secondary" (click)="editarSugestaoIndividual(sugestaoSelecionada)">
                <i class="fa-solid fa-edit"></i>
                Editar
              </button>
              <button class="action-btn tertiary" (click)="regenerarSugestao()" [disabled]="carregando">
                <i class="fa-solid fa-arrows-rotate"></i>
                Nova
              </button>
              <button class="action-btn success" (click)="enviarSugestao()" *ngIf="false">
                <i class="fa-solid fa-paper-plane"></i>
                Enviar
              </button>
            </div>

            <!-- Navegação entre sugestões (se houver múltiplas) -->
            <div class="suggestion-navigation" *ngIf="sugestoes.length > 1">
              <button class="nav-btn"
                      (click)="selecionarSugestao(sugestaoSelecionada - 1)"
                      [disabled]="sugestaoSelecionada === 0">
                <i class="fa-solid fa-chevron-left"></i>
              </button>
              <span class="nav-info">
                {{ sugestaoSelecionada + 1 }} de {{ sugestoes.length }}
              </span>
              <button class="nav-btn"
                      (click)="selecionarSugestao(sugestaoSelecionada + 1)"
                      [disabled]="sugestaoSelecionada === sugestoes.length - 1">
                <i class="fa-solid fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Respostas Rápidas -->
        <div class="quick-responses" *ngIf="!carregando">
          <div class="quick-responses-label">🎯 RESPOSTAS RÁPIDAS:</div>
          <div class="quick-response-buttons">
            <button class="quick-btn" (click)="gerarRespostaRapida('roi')">
              💰 Falar ROI
            </button>
            <button class="quick-btn" (click)="gerarRespostaRapida('case')">
              📊 Mostrar Case
            </button>
            <button class="quick-btn" (click)="gerarRespostaRapida('demo')">
              📅 Demo
            </button>
          </div>
        </div>

        <!-- Observações da IA -->
        <div *ngIf="faseDetectadaAutomaticamente" class="ai-observation">
          <i class="fa-solid fa-lightbulb"></i>
          {{ faseDetectadaAutomaticamente }}
        </div>
      </div>
    </div>


    <!-- 4. CONTEXTO RÁPIDO (Colapsável) -->
    <div class="context-section">
      <div class="section-header" (click)="toggleSection('context')">
        <h6 class="section-title">
          <i class="fa-solid fa-chart-line"></i>
          Contexto da Conversa
        </h6>
        <i class="fa-solid fa-chevron-down toggle-icon"
           [ngClass]="{'rotated': !sectionsCollapsed.context}"></i>
      </div>

      <div class="section-content" [ngClass]="{'collapsed': sectionsCollapsed.context}">
        <div class="context-info">
          <!-- Gatilhos Identificados -->
          <div class="triggers-section">
            <div class="subsection-title">🎯 GATILHOS IDENTIFICADOS:</div>
            <div class="trigger-list">
              <span class="trigger-item">• "Taxa muito alta" 💰</span>
              <span class="trigger-item">• "Comendo lucro" 📉</span>
              <span class="trigger-item">• "Frustração" 😤</span>
            </div>
          </div>

          <!-- Sentimento da Conversa -->
          <div class="sentiment-section">
            <div class="subsection-title">😊 SENTIMENTO:</div>
            <div class="sentiment-flow">
              <span class="sentiment-from">Frustrado</span>
              <i class="fa-solid fa-arrow-right"></i>
              <span class="sentiment-to">Interessado</span>
            </div>
          </div>

          <!-- Qualificação BANT -->
          <div class="qualification-section">
            <div class="subsection-title">✅ QUALIFICAÇÃO:</div>
            <div class="bant-indicators">
              <span class="bant-item qualified">Budget: ✅</span>
              <span class="bant-item qualified">Authority: ✅</span>
              <span class="bant-item qualified">Need: ✅</span>
              <span class="bant-item qualified">Time: ✅</span>
            </div>
          </div>

          <!-- Próximos Passos -->
          <div class="next-steps-section">
            <div class="subsection-title">🎯 PRÓXIMOS PASSOS:</div>
            <div class="steps-list">
              <div class="step-item">1. Explorar impacto financeiro</div>
              <div class="step-item">2. Mostrar economia potencial</div>
              <div class="step-item">3. Agendar demonstração</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 5. AÇÕES RÁPIDAS -->
    <div class="actions-section">
      <div class="section-header">
        <h6 class="section-title">
          <i class="fa-solid fa-bolt"></i>
          Ações Rápidas
        </h6>
      </div>

      <div class="section-content">
        <div class="action-buttons">
          <button class="action-button schedule" (click)="abrirAgendamento()">
            <i class="fa-solid fa-calendar-days"></i>
            <div class="action-info">
              <span class="action-title">Agendar Demo</span>
              <small class="action-subtitle">Slots: Hoje 16h, Amanhã 10h</small>
            </div>
          </button>

          <button class="action-button proposal" (click)="enviarProposta()">
            <i class="fa-solid fa-file-invoice-dollar"></i>
            <div class="action-info">
              <span class="action-title">Enviar Proposta</span>
              <small class="action-subtitle">Template: Economia iFood</small>
            </div>
          </button>

          <button class="action-button call" (click)="iniciarLigacao()">
            <i class="fa-solid fa-phone"></i>
            <div class="action-info">
              <span class="action-title">Ligar Agora</span>
              <small class="action-subtitle">Status: Online</small>
            </div>
          </button>

          <button class="action-button followup" (click)="configurarFollowup()">
            <i class="fa-solid fa-bell"></i>
            <div class="action-info">
              <span class="action-title">Follow-up</span>
              <small class="action-subtitle">Sugestão: 1 dia</small>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 6. NOTAS RÁPIDAS -->
    <div class="notes-section">
      <div class="section-header">
        <h6 class="section-title">
          <i class="fa-solid fa-sticky-note"></i>
          Notas Rápidas
        </h6>
      </div>

      <div class="section-content">
        <div class="notes-input">
          <textarea class="notes-textarea"
                    [(ngModel)]="notasRapidas"
                    placeholder="Interessado em economia. Paga R$ 2.500/mês em taxas iFood. Frustrado com custos. Próximo: mostrar ROI e agendar demo."
                    rows="4"></textarea>
        </div>

        <div class="notes-actions">
          <button class="notes-btn save" (click)="salvarNotas()">
            <i class="fa-solid fa-floppy-disk"></i>
            Salvar
          </button>
          <button class="notes-btn copy" (click)="copiarNotas()">
            <i class="fa-solid fa-copy"></i>
            Copiar
          </button>
          <button class="notes-btn ai" (click)="resumirComIA()">
            <i class="fa-solid fa-robot"></i>
            IA Resumir
          </button>
        </div>

        <!-- Histórico de Notas -->
        <div class="notes-history">
          <div class="history-title">📚 HISTÓRICO:</div>
          <div class="history-list">
            <div class="history-item">
              <span class="history-time">• 15:30</span>
              <span class="history-text">Primeira conversa</span>
            </div>
            <div class="history-item">
              <span class="history-time">• 15:35</span>
              <span class="history-text">Identificou problema taxas</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Captura Rápida (só aparece para não cadastrados) -->
    <div class="quick-capture-section" *ngIf="!contato.id">
      <div class="section-header">
        <h6 class="section-title">
          <i class="fa-solid fa-user-plus"></i>
          Cadastrar Lead
        </h6>
      </div>

      <div class="section-content">
        <div class="capture-form">
          <div class="form-group">
            <label class="form-label">Nome do contato</label>
            <input type="text" class="form-input"
                   [value]="nomeWhatsApp" readonly>
          </div>
          <div class="form-group">
            <label class="form-label">Nome da empresa</label>
            <input type="text" class="form-input"
                   placeholder="Ex: Restaurante do João"
                   [(ngModel)]="novoLead.empresa">
          </div>
          <div class="form-group">
            <label class="form-label">Email</label>
            <input type="email" class="form-input"
                   placeholder="<EMAIL>"
                   [(ngModel)]="novoLead.email">
          </div>
          <div class="form-group">
            <label class="form-label">Observações rápidas</label>
            <textarea class="form-textarea"
                      placeholder="Ex: Interessado em cardápio digital, tem 2 lojas..."
                      [(ngModel)]="novoLead.observacoes"
                      rows="3"></textarea>
          </div>
          <div class="form-actions">
            <button class="form-btn primary" (click)="cadastrarLeadRapido()">
              <i class="fa-solid fa-floppy-disk"></i>
              Salvar e Cadastrar Lead
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- 7. STATUS (Footer) -->
  <div class="status-footer">
    <div class="status-indicators">
      <span class="status-item online">
        <i class="fa-solid fa-circle"></i>
        Sincronizado
      </span>
      <span class="status-item">
        <i class="fa-solid fa-robot"></i>
        IA Ativa
      </span>
      <span class="status-item">
        <i class="fa-solid fa-clock"></i>
        Atualizado 1s
      </span>
    </div>
  </div>
</div>
