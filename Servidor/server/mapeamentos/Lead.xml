<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Lead">

  <!-- ResultMap básico -->
  <resultMap id="leadRM" type="Lead">
    <id property="id" column="crm_lead_id"/>

    <!-- Mapeamento da FK para empresa CRM -->
    <result property="crmEmpresaId" column="crm_lead_crm_empresa_id"/>

    <result property="nomeResponsavel" column="crm_lead_nome_responsavel"/>
    <result property="empresa" column="crm_lead_empresa"/>
    <result property="telefone" column="crm_lead_telefone"/>
    <result property="endereco" column="crm_lead_endereco"/>
    <result property="instagramHandle" column="crm_lead_instagram"/>
    <result property="linkInsta" column="crm_lead_link_insta"/>
    <result property="bioInsta" column="crm_lead_bio_insta"/>

    <result property="etapa" column="crm_lead_etapa"/>
    <result property="score" column="crm_lead_score"/>
    <result property="origem" column="crm_lead_origem"/>
    <result property="segmento" column="crm_lead_segmento"/>

    <result property="dataCriacao" column="crm_lead_data_criacao"/>
    <result property="dataUltimaInteracao" column="crm_lead_data_ultima_interacao"/>
    <result property="dataProximoFollowup" column="crm_lead_data_proximo_followup"/>

    <!-- Demais campos opcionais mapeados de forma direta -->
    <result property="valorPotencial" column="crm_lead_valor_potencial"/>
    <result property="vendedorId" column="crm_lead_vendedor_id"/>
    <result property="motivoPerda" column="crm_lead_motivo_perda"/>
    <result property="dataFechamento" column="crm_lead_data_fechamento"/>

    <!-- Campos de enriquecimento Instagram -->
    <result property="instagramData.followers" column="crm_lead_seguidores"/>
    <result property="instagramData.following" column="crm_lead_seguindo"/>
    <result property="instagramData.accountType" column="crm_lead_tipo_conta_instagram"/>
    <result property="instagramData.businessCategory" column="crm_lead_categoria_instagram"/>
    <result property="instagramData.location" column="crm_lead_localizacao"/>
    <result property="instagramData.website" column="crm_lead_site"/>

    <!-- Campo website direto (compatibilidade com frontend) -->
    <result property="website" column="crm_lead_site"/>

    <!-- Campos de IA -->
    <result property="rapport.tipoNegocio" column="crm_lead_tipo_negocio_detectado"/>
    <result property="rapport.pontosDor" column="crm_lead_pontos_dor"/>
    <result property="faseSpinDetectada" column="crm_lead_fase_spin_detectada"/>
    <result property="confiancaFaseDetectada" column="crm_lead_confianca_fase_detectada"/>
    <result property="dataUltimaDeteccaoFase" column="crm_lead_data_ultima_deteccao_fase"/>

    <!-- Observações -->
    <result property="notas" column="crm_lead_notas"/>
    <result property="observacoes" column="crm_lead_observacoes"/>

    <!-- Associação com objeto CrmEmpresa -->
    <association property="crmEmpresa" resultMap="CrmEmpresa.crmEmpresaRM"/>

    <!-- Collection de links do lead -->
    <collection property="links" resultMap="LeadLink.leadLinkRM"/>
  </resultMap>

  <!-- Seleção com filtros simples -->
  <select id="selecione" parameterType="map" resultMap="leadRM" prefix="true">
    select * from crm_lead
    join crm_empresa on crm_empresa.id = crm_lead.crm_empresa_id
    left join crm_lead_link on crm_lead_link.crm_lead_id = crm_lead.id
    where 1=1
    <if test="id != null">
      and crm_lead.id = #{id}
    </if>
    <if test="texto != null">
      and (crm_lead.nome_responsavel like concat('%', #{texto}, '%')
        or crm_empresa.nome like concat('%', #{texto}, '%')
        or crm_lead.telefone like concat('%', #{texto}, '%')
        or crm_lead.instagram like concat('%', #{texto}, '%'))
    </if>
    <if test="etapa != null">
      and crm_lead.etapa = #{etapa}
    </if>
    <if test="telefone != null">
      and crm_lead.telefone = #{telefone}
    </if>
    order by crm_lead.data_criacao desc
    <if test="inicio != null">
      limit #{inicio}, #{total}
    </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    select count(distinct crm_lead.id) from crm_lead
    join crm_empresa on crm_empresa.id = crm_lead.crm_empresa_id
    where 1=1
    <if test="crmEmpresaId != null">
      and crm_lead.crm_empresa_id = #{crmEmpresaId}
    </if>
    <if test="texto != null">
      and (crm_lead.nome_responsavel like concat('%', #{texto}, '%')
        or crm_empresa.nome like concat('%', #{texto}, '%')
        or crm_lead.telefone like concat('%', #{texto}, '%')
        or crm_lead.instagram like concat('%', #{texto}, '%'))
    </if>
  </select>

  <!-- Inserção básica -->
  <insert id="insira" parameterType="Lead" useGeneratedKeys="true" keyProperty="id">
    insert into crm_lead (
      crm_empresa_id, nome_responsavel, telefone, endereco, etapa, origem, bio_insta, data_criacao,
      data_ultima_interacao, data_proximo_followup, score, segmento,
      valor_potencial, vendedor_id, instagram, link_insta, seguidores, seguindo,
      tipo_conta_instagram, categoria_instagram, localizacao, site, avatar_url,
      tipo_negocio_detectado, pontos_dor,
      relatorio_ia_json, notas, observacoes, created_at, updated_at
    ) values (
      #{crmEmpresaId}, #{nomeResponsavel}, #{telefone}, #{endereco}, #{etapa}, #{origem}, #{bioInsta}, now(),
      #{dataUltimaInteracao}, #{dataProximoFollowup}, #{score}, #{segmento},
      #{valorPotencial}, #{vendedorId}, #{instagramHandle}, #{linkInsta}, #{instagramData.followers}, #{instagramData.following},
      #{instagramData.accountType}, #{instagramData.businessCategory}, #{instagramData.location},
      <choose>
        <when test="website != null">#{website}</when>
        <otherwise>#{instagramData.website}</otherwise>
      </choose>, #{avatarUrl},
      #{rapport.tipoNegocio}, #{rapport.pontosDor},
      #{relatorioIaJson}, #{notas}, #{observacoes}, now(), now()
    )
  </insert>

  <!-- Atualização básica -->
  <update id="atualize" parameterType="Lead">
    update crm_lead set
      nome_responsavel = #{nomeResponsavel},
      telefone = #{telefone},
      endereco = #{endereco},
      etapa = #{etapa},
      origem = #{origem},
      bio_insta = #{bioInsta},
      score = #{score},
      segmento = #{segmento},
      valor_potencial = #{valorPotencial},
      vendedor_id = #{vendedorId},
      instagram = #{instagramHandle},
      link_insta = #{linkInsta},
      seguidores = #{instagramData.followers},
      seguindo = #{instagramData.following},
      tipo_conta_instagram = #{instagramData.accountType},
      categoria_instagram = #{instagramData.businessCategory},
      localizacao = #{instagramData.location},
      site = <choose>
        <when test="website != null">#{website}</when>
        <otherwise>#{instagramData.website}</otherwise>
      </choose>,
      avatar_url = #{avatarUrl},
      tipo_negocio_detectado = #{rapport.tipoNegocio},
      pontos_dor = #{rapport.pontosDor},
      relatorio_ia_json = #{relatorioIaJson},
      notas = #{notas},
      observacoes = #{observacoes},
      data_ultima_interacao = #{dataUltimaInteracao},
      data_proximo_followup = #{dataProximoFollowup},
      motivo_perda = #{motivoPerda},
      data_fechamento = #{dataFechamento},
      updated_at = now()
    where id = #{id}
  </update>

  <!-- Remoção física (poderia ser lógica adicionando coluna removido) -->
  <delete id="remova" parameterType="map">
    delete from crm_lead where id = #{id}
  </delete>

</mapper>
